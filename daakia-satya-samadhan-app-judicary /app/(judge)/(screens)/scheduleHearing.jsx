import React, { useState, useRef, useCallback, useMemo, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  Animated,
  Platform,
  Modal,
  StatusBar
} from 'react-native';
import { Calendar } from 'react-native-big-calendar';
import { Ionicons } from '@expo/vector-icons';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { Colors } from '../../../constants/colors';
import { styles, DRAWER_WIDTH, IS_TABLET, SCREEN_WIDTH, SCREEN_HEIGHT } from '../../../components/Larges/CalendarCompo/styles/scheduleHearing.styles';
import MiniCalendar from '../../../components/Larges/CalendarCompo/MiniCalendar';
import AgendaView from '../../../components/Larges/CalendarCompo/AgendaView';
import CreateEventModal from '../../../components/Larges/CalendarCompo/CreateEventModal';
import { useLocalSearchParams } from 'expo-router';
import { apiService } from '../../../services/api';
import { storageService } from '../../../services/storage';
import useUploadMedia from '../../../hooks/useUploadMedia';

const ScheduleHearing = () => {
  const params = useLocalSearchParams();
  const { uploadMedia, isUploading } = useUploadMedia();

  // Get caseId from params
  const caseId = params.caseId;

  const [selectedDate, setSelectedDate] = useState(new Date());
  const [viewMode, setViewMode] = useState('week');
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const [showViewSelector, setShowViewSelector] = useState(false);
  const [viewYear, setViewYear] = useState(new Date().getFullYear());
  const insets = useSafeAreaInsets();

  // Ref for the view selector button to position the dropdown
  const weekButtonRef = useRef(null);
  const [dropdownPosition, setDropdownPosition] = useState({ top: 100, right: 20 });

  // Event creation state
  const [showCreateEventModal, setShowCreateEventModal] = useState(false);
  const [newEventStartDate, setNewEventStartDate] = useState(null);
  const [newEventEndDate, setNewEventEndDate] = useState(null);
  const [editingEvent, setEditingEvent] = useState(null);

  // For drawer animation
  const translateX = useRef(new Animated.Value(-DRAWER_WIDTH)).current;

  // Check for openCreateModal parameter on mount
  useEffect(() => {
    if (params.openCreateModal === 'true') {
      const now = new Date();
      setNewEventStartDate(now);
      const endDate = new Date(now.getTime() + 60 * 60 * 1000);
      setNewEventEndDate(endDate);
      setShowCreateEventModal(true);
    }
  }, [params.openCreateModal]);

  // Set initial drawer state based on device type
  useEffect(() => {
    if (IS_TABLET) {
      // Open drawer by default on tablets
      setIsDrawerOpen(true);
      translateX.setValue(0);
    }
  }, []);

  // Available calendar view modes
  const viewModes = [
    { id: 'week', label: 'Week' },
    { id: 'day', label: 'Day' },
    { id: 'month', label: 'Month' },
    { id: 'year', label: 'Year' },
    { id: 'agenda', label: 'Agenda' },
  ];

  // Events state - will be populated from API
  const [events, setEvents] = useState([]);
  const [loading, setLoading] = useState(false);

  // TODO: Add API integration to fetch events
  useEffect(() => {
    // This will be replaced with actual API call to fetch events
    // fetchEvents();
  }, []);

  // Format date range for header
  const formatDateRange = useCallback(() => {
    const isCompact = !IS_TABLET; // Use compact format on mobile

    if (viewMode === 'day') {
      return selectedDate.toLocaleDateString('default', {
        day: 'numeric',
        month: isCompact ? 'short' : 'long',
        year: isCompact ? '2-digit' : 'numeric',
        weekday: isCompact ? 'short' : 'long'
      });
    } else if (viewMode === 'month') {
      return selectedDate.toLocaleDateString('default', {
        month: 'long',
        year: isCompact ? '2-digit' : 'numeric'
      });
    } else if (viewMode === 'year') {
      return selectedDate.getFullYear().toString();
    } else if (viewMode === 'agenda') {
      return 'Upcoming Events';
    } else {
      // Week view (default)
      const startOfWeek = new Date(selectedDate);
      startOfWeek.setDate(selectedDate.getDate() - selectedDate.getDay() + 1);

      const endOfWeek = new Date(startOfWeek);
      endOfWeek.setDate(startOfWeek.getDate() + 6);

      if (isCompact) {
        // Compact format for mobile: "1-7 Jan '23"
        return `${startOfWeek.getDate()}-${endOfWeek.getDate()} ${startOfWeek.toLocaleString('default', { month: 'short' })} '${startOfWeek.getFullYear().toString().slice(-2)}`;
      } else {
        // Full format for tablets: "1-7 January 2023"
        return `${startOfWeek.getDate()}-${endOfWeek.getDate()} ${startOfWeek.toLocaleString('default', { month: 'long' })} ${startOfWeek.getFullYear()}`;
      }
    }
  }, [selectedDate, viewMode]);

  // Format time for event display
  const formatEventTime = useCallback((date) => {
    if (!date) return '';

    // Handle both Date objects and timestamp numbers
    const dateObj = date instanceof Date ? date : new Date(date);

    // Check if the date is valid
    if (isNaN(dateObj.getTime())) return '';

    return dateObj.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  }, []);

  // Toggle drawer open/close
  const toggleDrawer = useCallback(() => {
    if (isDrawerOpen) {
      // Close drawer
      Animated.timing(translateX, {
        toValue: -DRAWER_WIDTH,
        duration: 250,
        useNativeDriver: true,
      }).start(() => {
        setIsDrawerOpen(false);
      });
    } else {
      // Open drawer
      setIsDrawerOpen(true);

      // Animate drawer
      Animated.timing(translateX, {
        toValue: 0,
        duration: 250,
        useNativeDriver: true,
      }).start();
    }
  }, [isDrawerOpen, translateX]);

  // We're using toggleDrawer for all drawer operations

  // Handle date selection from mini calendar
  const handleDateSelect = useCallback((date) => {
    if (date) {
      setSelectedDate(date);
      // Close drawer on mobile only
      if (!IS_TABLET) {
        toggleDrawer();
      }
    }
  }, [toggleDrawer, IS_TABLET]);

  // Handle today button press
  const handleTodayPress = useCallback(() => {
    setSelectedDate(new Date());
  }, []);

  // Handle cell press to create a new event
  const handleCellPress = useCallback((date) => {
    // Set the start time to the clicked time
    setNewEventStartDate(date);

    // Set the end time to 1 hour after the start time
    const endDate = new Date(date.getTime() + 60 * 60 * 1000);
    setNewEventEndDate(endDate);
    setEditingEvent(null); // Clear any editing event
    setShowCreateEventModal(true);
  }, []);

  // Handle event press to edit an existing event
  const handleEventPress = useCallback((event) => {
    setEditingEvent(event);
    setNewEventStartDate(event.start);
    setNewEventEndDate(event.end);
    setShowCreateEventModal(true);
  }, []);

  // Handle saving a new event or updating an existing one - using API
  const handleSaveEvent = useCallback(async (eventData) => {
    try {
      setLoading(true);

      // Get auth token
      const token = await storageService.getToken();

      // Handle file upload if attachment exists
      let attachmentUrl = null;
      if (eventData.attachment && eventData.attachment.uri) {
        console.log('Uploading attachment...');
        attachmentUrl = await uploadMedia(eventData.attachment.uri, eventData.attachment.type);
        if (!attachmentUrl) {
          throw new Error('Failed to upload attachment');
        }
      }

      // Prepare event data for API
      const apiEventData = {
        title: eventData.title,
        description: eventData.description || '',
        caseId: caseId, // Get from params
        startDateTime: eventData.start.toISOString(),
        endDateTime: eventData.end.toISOString(),
        duration: Math.round((eventData.end - eventData.start) / (1000 * 60)), // in minutes
        eventColor: eventData.color || '#0B36A1',
        attachmentUrl: attachmentUrl
      };

      console.log('Creating event with data:', apiEventData);

      // Create event via API
      const response = await apiService.createEvent(token, apiEventData);
      console.log('Event created successfully:', response);

      // Close modal and clear editing state
      setShowCreateEventModal(false);
      setEditingEvent(null);

      // TODO: Refresh events list or add the new event to local state
      // For now, just log success
      console.log('Event creation completed');

    } catch (error) {
      console.error('Error creating event:', error.message);
      // TODO: Show error message to user
    } finally {
      setLoading(false);
    }
  }, [editingEvent, caseId, uploadMedia]);

  // Handle event press from sidebar
  const handleSidebarEventPress = useCallback((event) => {
    handleEventPress(event);
  }, [handleEventPress]);

  // Handle view mode selection
  const handleViewModeSelect = useCallback((mode) => {
    setViewMode(mode);
    setShowViewSelector(false);

    // Update viewYear when switching to year view
    if (mode === 'year') {
      setViewYear(selectedDate.getFullYear());
    }

    // Close drawer if switching to table view on mobile
    if (!IS_TABLET && mode === 'agenda' && isDrawerOpen) {
      toggleDrawer();
    }
  }, [toggleDrawer, selectedDate, isDrawerOpen, IS_TABLET]);

  // Toggle view selector modal
  const toggleViewSelector = useCallback(() => {
    if (!showViewSelector && weekButtonRef.current) {
      // Measure the position of the button to place the dropdown correctly
      weekButtonRef.current.measure((x, y, width, height, pageX, pageY) => {
        setDropdownPosition({
          top: pageY + height + 5, // Position below the button with a small gap
          left: pageX, // Align with the left edge of the button
        });
      });
    }
    setShowViewSelector(prev => !prev);
  }, [showViewSelector]);

  // Custom drawer content
  const renderDrawerContent = () => {
    // Helper function to check if a date is today
    const isToday = (date) => {
      const today = new Date();
      return date.getDate() === today.getDate() &&
        date.getMonth() === today.getMonth() &&
        date.getFullYear() === today.getFullYear();
    };

    // Helper function to check if a date is tomorrow
    const isTomorrow = (date) => {
      const tomorrow = new Date();
      tomorrow.setDate(tomorrow.getDate() + 1);
      return date.getDate() === tomorrow.getDate() &&
        date.getMonth() === tomorrow.getMonth() &&
        date.getFullYear() === tomorrow.getFullYear();
    };

    // Group events by day
    const groupedEvents = {
      today: events.filter(event => isToday(new Date(event.start))),
      tomorrow: events.filter(event => isTomorrow(new Date(event.start))),
      upcoming: events.filter(event => {
        const eventDate = new Date(event.start);
        return !isToday(eventDate) && !isTomorrow(eventDate) && eventDate > new Date();
      })
    };

    // Sort events by time within each group
    Object.keys(groupedEvents).forEach(key => {
      groupedEvents[key].sort((a, b) => new Date(a.start) - new Date(b.start));
    });

    // Format time (e.g., "09:00")
    const formatTimeShort = (date) => {
      const hours = date.getHours().toString().padStart(2, '0');
      const minutes = date.getMinutes().toString().padStart(2, '0');
      return `${hours}:${minutes}`;
    };

    // Format date range for upcoming events
    const formatDateRange = (startDate, endDate) => {
      const start = new Date(startDate);
      const end = new Date(endDate);

      const startDay = start.getDate().toString().padStart(2, '0');
      const startMonth = (start.getMonth() + 1).toString().padStart(2, '0');

      const endDay = end.getDate().toString().padStart(2, '0');
      const endMonth = (end.getMonth() + 1).toString().padStart(2, '0');

      return `${startDay}-${startMonth} to ${endDay}-${endMonth}`;
    };

    return (
      <View style={styles.drawerContainer}>
        {/* Mini calendar in drawer */}
        <MiniCalendar
          onDateSelect={handleDateSelect}
          selectedDate={selectedDate}
        />

        {/* Events list */}
        <ScrollView style={styles.eventsContainer} showsVerticalScrollIndicator={false}>
          {/* Today's events */}
          {groupedEvents.today.length > 0 && (
            <View style={styles.daySection}>
              <View style={styles.daySectionHeader}>
                <Ionicons name="calendar-outline" size={20} color={Colors.primary} style={styles.daySectionIcon} />
                <Text style={styles.daySectionTitle}>Today</Text>
              </View>

              {groupedEvents.today.map((event, index) => (
                <TouchableOpacity
                  key={`today-${index}`}
                  style={styles.eventListItem}
                  onPress={() => handleSidebarEventPress(event)}
                  activeOpacity={0.7}
                >
                  <View style={[styles.eventDot, { backgroundColor: event.color }]} />
                  <Text style={styles.eventListTitle} numberOfLines={1}>{event.title}</Text>
                  <Text style={styles.eventListTime}>{formatTimeShort(new Date(event.start))}</Text>
                </TouchableOpacity>
              ))}
            </View>
          )}

          {/* Tomorrow's events */}
          {groupedEvents.tomorrow.length > 0 && (
            <View style={styles.daySection}>
              <View style={styles.daySectionHeader}>
                <Ionicons name="calendar-outline" size={20} color={Colors.primary} style={styles.daySectionIcon} />
                <Text style={styles.daySectionTitle}>Tomorrow</Text>
              </View>

              {groupedEvents.tomorrow.map((event, index) => (
                <TouchableOpacity
                  key={`tomorrow-${index}`}
                  style={styles.eventListItem}
                  onPress={() => handleSidebarEventPress(event)}
                  activeOpacity={0.7}
                >
                  <View style={[styles.eventDot, { backgroundColor: event.color }]} />
                  <Text style={styles.eventListTitle} numberOfLines={1}>{event.title}</Text>
                  <Text style={styles.eventListTime}>{formatTimeShort(new Date(event.start))}</Text>
                </TouchableOpacity>
              ))}
            </View>
          )}

          {/* Upcoming events (beyond tomorrow) */}
          {groupedEvents.upcoming.length > 0 && (
            <View style={styles.daySection}>
              <View style={styles.daySectionHeader}>
                <Ionicons name="calendar-outline" size={20} color={Colors.primary} style={styles.daySectionIcon} />
                <Text style={styles.daySectionTitle}>Upcoming</Text>
              </View>

              {groupedEvents.upcoming.map((event, index) => (
                <TouchableOpacity
                  key={`upcoming-${index}`}
                  style={styles.eventListItem}
                  onPress={() => handleSidebarEventPress(event)}
                  activeOpacity={0.7}
                >
                  <View style={[styles.eventDot, { backgroundColor: event.color }]} />
                  <Text style={styles.eventListTitle} numberOfLines={1}>{event.title}</Text>
                  <Text style={styles.eventListTime}>{formatTimeShort(new Date(event.start))}</Text>
                </TouchableOpacity>
              ))}
            </View>
          )}

          {/* Date range at the bottom */}
          {events.length > 0 && (
            <Text style={styles.dateRangeText}>
              {formatDateRange(
                events.reduce((min, e) => new Date(e.start) < min ? new Date(e.start) : min, new Date()),
                events.reduce((max, e) => new Date(e.end) > max ? new Date(e.end) : max, new Date())
              )}
            </Text>
          )}

          {/* No events message */}
          {events.length === 0 && (
            <View style={styles.noEventsContainer}>
              <Ionicons name="calendar-outline" size={48} color="#ccc" />
              <Text style={styles.noEventsText}>No upcoming events</Text>
            </View>
          )}
        </ScrollView>
      </View>
    );
  };

  // Reusable MonthCalendar component
  const MonthCalendar = useCallback(({ month, year, isSelected, onPress }) => {
    // Function to check if a date has events
    const dateHasEvents = (year, month, day) => {
      return events.some(event => {
        const eventDate = event.start instanceof Date ? event.start : new Date(event.start);
        return (
          eventDate.getFullYear() === year &&
          eventDate.getMonth() === month &&
          eventDate.getDate() === day
        );
      });
    };

    // Function to generate days for a month calendar
    const generateMonthDays = () => {
      const firstDay = new Date(year, month, 1);
      const lastDay = new Date(year, month + 1, 0);

      const days = [];
      const daysInMonth = lastDay.getDate();

      // Get the day of the week for the first day (0 = Sunday, 1 = Monday, etc.)
      // For our calendar, we want Monday as day 0, so we need to adjust
      let firstDayOfWeek = firstDay.getDay(); // 0 (Sunday) to 6 (Saturday)

      // Convert to Monday-based index (0 = Monday, 6 = Sunday)
      firstDayOfWeek = firstDayOfWeek === 0 ? 6 : firstDayOfWeek - 1;

      // Add empty cells for days before the first day of the month
      for (let i = 0; i < firstDayOfWeek; i++) {
        days.push({ day: '', empty: true });
      }

      // Add days of the month
      for (let i = 1; i <= daysInMonth; i++) {
        const date = new Date(year, month, i);
        const isToday =
          date.getDate() === new Date().getDate() &&
          date.getMonth() === new Date().getMonth() &&
          date.getFullYear() === new Date().getFullYear();

        const hasEvent = dateHasEvents(year, month, i);

        days.push({
          day: i,
          date,
          isToday,
          hasEvent
        });
      }

      // Add empty cells at the end to complete the grid if needed
      // This ensures we have complete weeks
      const totalCells = Math.ceil(days.length / 7) * 7;
      const emptyCellsToAdd = totalCells - days.length;

      for (let i = 0; i < emptyCellsToAdd; i++) {
        days.push({ day: '', empty: true });
      }

      return days;
    };

    const monthName = new Date(year, month, 1).toLocaleString('default', { month: 'long' });
    const days = generateMonthDays();

    // Calculate optimal day cell size based on screen width and number of columns
    const getDayCellSize = () => {
      // Get the number of columns that will be used in the year view
      const getColumns = () => {
        if (SCREEN_WIDTH >= 1200) return 4; // Large screens - 4 columns
        if (SCREEN_WIDTH >= 768) return 3;  // Medium screens - 3 columns
        if (SCREEN_WIDTH >= 500) return 2;  // Small screens - 2 columns
        return 1;                           // Very small screens - 1 column
      };

      const columns = getColumns();

      // For single column layout (mobile), calculate width based on screen width
      if (columns === 1) {
        // Calculate available width for the calendar (screen width minus padding)
        const availableWidth = SCREEN_WIDTH - 40; // 20px padding on each side
        const cellWidth = Math.floor((availableWidth - 14) / 7); // 7 days, minus some margin
        return {
          width: cellWidth,
          height: cellWidth, // Make it square
          fontSize: Math.min(cellWidth - 4, 14) // Ensure text fits
        };
      }

      // For larger screens, use fixed sizes
      if (SCREEN_WIDTH >= 1200) return { width: 24, height: 24, fontSize: 14 };
      if (SCREEN_WIDTH >= 768) return { width: 22, height: 22, fontSize: 13 };
      if (SCREEN_WIDTH >= 500) return { width: 20, height: 20, fontSize: 12 };
      return { width: 18, height: 18, fontSize: 11 };
    };

    const { width, height, fontSize } = getDayCellSize();

    return (
      <TouchableOpacity
        style={[
          styles.monthCard,
          isSelected && styles.selectedMonthCard
        ]}
        onPress={onPress}
        activeOpacity={0.7}
      >
        <Text style={[
          styles.monthName,
          isSelected && styles.selectedMonthName
        ]}>
          {monthName}
        </Text>

        <View style={styles.miniCalendarContainer}>
          <View style={styles.miniDaysHeader}>
            {['M', 'T', 'W', 'T', 'F', 'S', 'S'].map((day, index) => (
              <Text key={index} style={[
                styles.miniDayLetter,
                { width: width }
              ]}>
                {day}
              </Text>
            ))}
          </View>

          <View style={styles.miniCalendarGrid}>
            {days.map((item, index) => (
              <View
                key={index}
                style={[
                  styles.miniCalendarDay,
                  { width: width, height: height },
                  // Check if this date is the selected date
                  (selectedDate.getDate() === item.day &&
                   selectedDate.getMonth() === month &&
                   selectedDate.getFullYear() === year) && styles.selectedCell,
                  item.empty && styles.miniEmptyCell
                ]}
              >
                {!item.empty && (
                  <>
                    <Text style={[
                      styles.miniCalendarDayText,
                      { fontSize: fontSize },
                      item.isToday && styles.todayCellText,
                      // Apply selected text style if this is the selected date
                      (selectedDate.getDate() === item.day &&
                       selectedDate.getMonth() === month &&
                       selectedDate.getFullYear() === year) && styles.selectedCellText
                    ]}>
                      {item.day}
                    </Text>
                    {item.isToday && <View style={[
                      styles.todayDot,
                      { width: fontSize / 3, height: fontSize / 3 }
                    ]} />}
                  </>
                )}
              </View>
            ))}
          </View>
        </View>
      </TouchableOpacity>
    );
  }, [events, SCREEN_WIDTH]);

  // Render year view (custom view for "Year" mode)
  const renderYearView = () => {
    // Function to handle month selection
    const handleMonthSelect = (monthIndex) => {
      const newDate = new Date(viewYear, monthIndex, 1);
      setSelectedDate(newDate);
      setViewMode('month');
    };

    // Function to change year
    const changeYear = (increment) => {
      setViewYear(prevYear => prevYear + increment);
    };

    // Determine number of columns based on screen width
    const getNumColumns = () => {
      if (SCREEN_WIDTH >= 1200) return 4; // Large screens - 4 columns
      if (SCREEN_WIDTH >= 768) return 3;  // Medium screens - 3 columns
      if (SCREEN_WIDTH >= 500) return 2;  // Small screens - 2 columns
      return 1;                           // Very small screens - 1 column
    };

    const numColumns = getNumColumns();

    // Calculate optimal padding based on screen size
    const getPadding = () => {
      if (SCREEN_WIDTH >= 1200) return 12;
      if (SCREEN_WIDTH >= 768) return 10;
      if (SCREEN_WIDTH >= 500) return 8;
      return 6;
    };

    return (
      <View style={styles.yearContainer}>
        {/* Year selector */}
        <View style={styles.yearSelectorContainer}>
          <TouchableOpacity
            style={styles.yearNavigationButton}
            onPress={() => changeYear(-1)}
          >
            <Ionicons name="chevron-back" size={24} color={Colors.primary} />
          </TouchableOpacity>

          <Text style={styles.yearSelectorText}>{viewYear}</Text>

          <TouchableOpacity
            style={styles.yearNavigationButton}
            onPress={() => changeYear(1)}
          >
            <Ionicons name="chevron-forward" size={24} color={Colors.primary} />
          </TouchableOpacity>
        </View>

        {/* Month grids */}
        <ScrollView showsVerticalScrollIndicator={false} contentContainerStyle={{ paddingBottom: 20 }}>
          <View style={[styles.yearGrid, { width: '100%' }]}>
            {Array.from({ length: 12 }, (_, monthIndex) => (
              <View
                key={monthIndex}
                style={{
                  width: `${100 / numColumns}%`,
                  padding: getPadding()
                }}
              >
                <MonthCalendar
                  month={monthIndex}
                  year={viewYear}
                  isSelected={
                    selectedDate.getMonth() === monthIndex &&
                    selectedDate.getFullYear() === viewYear
                  }
                  onPress={() => handleMonthSelect(monthIndex)}
                />
              </View>
            ))}
          </View>
        </ScrollView>
      </View>
    );
  };

  // Render agenda view (custom view for "Agenda" mode)
  const renderAgendaView = () => (
    <AgendaView 
      events={events} 
      formatEventTime={formatEventTime} 
      onEventPress={handleEventPress}
    />
  );

  return (
    <View style={styles.pageContainer}>
      {/* Custom calendar header */}
      <View style={styles.calendarHeader}>
        <View style={styles.dateSelector}>
          <TouchableOpacity
            style={styles.menuButton}
            onPress={toggleDrawer}
          >
            <Ionicons
              name={isDrawerOpen ? "close" : "menu"}
              size={24}
              color="#333"
            />
          </TouchableOpacity>
          <Text style={styles.dateRangeText}>{formatDateRange()}</Text>
          <TouchableOpacity
            ref={weekButtonRef}
            style={styles.weekButton}
            onPress={toggleViewSelector}
          >
            <Text style={styles.weekButtonText}>
              {viewModes.find(mode => mode.id === viewMode)?.label || 'Week'}
            </Text>
            <Ionicons name="chevron-down" size={16} color={Colors.primary} />
          </TouchableOpacity>
        </View>

        <View style={styles.controls}>
          <TouchableOpacity
            style={styles.todayButton}
            onPress={handleTodayPress}
          >
            <Text style={styles.todayButtonText}>Today</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.addButton}
            onPress={() => {
              // Open event creation modal - event will be created via API
              const now = new Date();
              setNewEventStartDate(now);
              const endDate = new Date(now.getTime() + 60 * 60 * 1000);
              setNewEventEndDate(endDate);
              setShowCreateEventModal(true);
            }}
          >
            <Text style={styles.addButtonText}>Add event</Text>
            <Ionicons name="add" size={IS_TABLET ? 18 : 22} color="#fff" />
          </TouchableOpacity>
        </View>
      </View>

      {/* Main content area with drawer */}
      <View style={styles.contentContainer}>
        {/* Drawer - positioned absolutely for phones, side-by-side for tablets */}
        <Animated.View
          style={[
            styles.drawer,
            {
              transform: [{ translateX }],
              height: IS_TABLET ? SCREEN_HEIGHT - insets.top - 100 : '100%',
              top: 0,
              // For tablets, add border on the right side
              ...(IS_TABLET && {
                borderRightWidth: 1,
                borderRightColor: Colors.border,
              })
            }
          ]}
        >
          {renderDrawerContent()}
        </Animated.View>

        {/* Semi-transparent overlay when drawer is open (phones only) */}
        {isDrawerOpen && !IS_TABLET && (
          <TouchableOpacity
            style={styles.overlay}
            activeOpacity={1}
            onPress={toggleDrawer}
          />
        )}

        {/* Main calendar content - positioned correctly for both layouts */}
        <Animated.View
          style={[
            styles.mainContent,
            {
              // For tablets, use width and left positioning
              ...(IS_TABLET ? {
                width: isDrawerOpen ? SCREEN_WIDTH - DRAWER_WIDTH : SCREEN_WIDTH,
                left: isDrawerOpen ? DRAWER_WIDTH : 0
              } : {
                // For phones, keep content in place (drawer will overlay)
                width: SCREEN_WIDTH,
                left: 0,
                // No transform needed as drawer overlays content
                transform: [{ translateX: 0 }]
              })
            }
          ]}
        >
          {viewMode === 'agenda' ? (
            renderAgendaView()
          ) : viewMode === 'year' ? (
            renderYearView()
          ) : (
            <Calendar
              events={events}
              height={SCREEN_HEIGHT - 150}
              mode={viewMode}
              date={selectedDate}
              onPressCell={date => {
                // Update selected date and open event creation modal - event will be created via API
                setSelectedDate(date);
                handleCellPress(date);
              }}
              onPressEvent={handleEventPress}
              // Don't automatically update selected date when calendar view changes
              // This prevents the calendar from overriding our mini calendar selection
              onChangeDate={() => {}}
              weekStartsOn={1} // Start week on Monday
              showTime={true}
              swipeEnabled={true}
              style={styles.calendar}
              eventCellStyle={event => ({
                backgroundColor: event.color || Colors.primary,
                borderRadius: 4,
              })}
            />
          )}
        </Animated.View>
      </View>

      {/* View mode selector modal */}
      <Modal
        visible={showViewSelector}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setShowViewSelector(false)}
      >
        <TouchableOpacity
          style={styles.modalOverlay}
          activeOpacity={1}
          onPress={() => setShowViewSelector(false)}
        >
          <View
            style={[
              styles.viewSelectorContainer,
              dropdownPosition
            ]}
          >
            {viewModes.map(mode => (
              <TouchableOpacity
                key={mode.id}
                style={[
                  styles.viewOption,
                  viewMode === mode.id && styles.selectedViewOption
                ]}
                onPress={() => handleViewModeSelect(mode.id)}
              >
                <Text
                  style={[
                    styles.viewOptionText,
                    viewMode === mode.id && styles.selectedViewOptionText
                  ]}
                >
                  {mode.label}
                </Text>
                {viewMode === mode.id && (
                  <Ionicons name="checkmark" size={18} color={Colors.primary} />
                )}
              </TouchableOpacity>
            ))}
          </View>
        </TouchableOpacity>
      </Modal>

      {/* Create Event Modal */}
      <CreateEventModal
        visible={showCreateEventModal}
        onClose={() => {
          setShowCreateEventModal(false);
          setEditingEvent(null);
        }}
        onSave={handleSaveEvent}
        initialDate={newEventStartDate}
        initialEndDate={newEventEndDate}
        editingEvent={editingEvent}
        loading={loading || isUploading}
      />
    </View>
  );
};

export default ScheduleHearing;
